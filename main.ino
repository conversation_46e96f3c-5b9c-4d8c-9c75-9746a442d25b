const int soilSensor = 14;
const int pumpPin = 27;
int dryCal = 3600;
int wetCal = 1800;

//minimum reading in pure water was 1770, Max reading when dry in free air was 3600
int moistureLevel();

void setup(){
  Serial.begin(9600);
  pinMode(soilSensor, INPUT);
  pinMode(pumpPin, OUTPUT);
}

void loop(){
  moistureLevel();
  delay(500);
}

int moistureLevel(){
  int moistureLevel = analogRead(soilSensor);
  int percentage = map(moistureLevel, dryCal, wetCal, 0, 100);
  Serial.println(moistureLevel);
  Serial.println(percentage);
  return(moistureLevel);
}